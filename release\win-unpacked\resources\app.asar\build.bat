@echo off
echo ========================================
echo Password Manager - Desktop Build Script
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found: 
node --version

echo.
echo Installing dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Building application...
echo.

echo Building Windows installer...
call npm run build-win

if %errorlevel% neq 0 (
    echo ERROR: Failed to build Windows installer
    pause
    exit /b 1
)

echo.
echo Building portable version...
call npm run build-portable

if %errorlevel% neq 0 (
    echo ERROR: Failed to build portable version
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Files created in 'dist' folder:
echo - Password Manager Setup.exe (installer)
echo - PasswordManager-Portable-1.0.0.exe (portable)
echo.
echo You can now distribute these files.
echo.
pause
