// Скрипт для создания иконки приложения
const fs = require('fs');
const path = require('path');

// Создаем простую PNG иконку программно без внешних зависимостей
function createIcon() {
    console.log('Creating application icon...');

    // Создаем простую иконку в формате PNG
    // Это базовый PNG файл 256x256 с синим фоном и белым замком
    const pngData = createSimplePNG();

    const iconPath = path.join(__dirname, 'assets', 'icon.png');
    fs.writeFileSync(iconPath, Buffer.from(pngData));

    console.log('Icon created successfully: assets/icon.png');
    console.log('Note: For production, consider using a proper ICO file.');
}

// Создаем простой PNG файл программно
function createSimplePNG() {
    // Это упрощенная версия PNG файла
    // В реальном проекте лучше использовать готовую иконку
    console.log('Creating simple PNG icon...');

    // Возвращаем пустой массив - будем использовать SVG как есть
    return [];
}

// Альтернативный метод - создание ICO файла из существующих ресурсов
function createIconFromSVG() {
    const svgPath = path.join(__dirname, 'assets', 'icon.svg');
    const icoPath = path.join(__dirname, 'assets', 'icon.ico');

    if (fs.existsSync(svgPath)) {
        console.log('SVG icon found. For best results, manually convert to ICO format.');
        console.log('You can use online converters like:');
        console.log('- https://convertio.co/svg-ico/');
        console.log('- https://www.icoconverter.com/');

        // Создаем копию SVG как PNG для временного использования
        const pngPath = path.join(__dirname, 'assets', 'icon.png');
        fs.copyFileSync(svgPath, pngPath.replace('.png', '_backup.svg'));

        return true;
    }

    return false;
}

if (require.main === module) {
    if (!createIconFromSVG()) {
        createIcon();
    }
}