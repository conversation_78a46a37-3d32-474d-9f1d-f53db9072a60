// Функции для работы с документами
const saveDocument = async (documentData) => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['documents'], 'readwrite');
        const store = transaction.objectStore('documents');
        
        return new Promise((resolve, reject) => {
            const request = documentData.id 
                ? store.put({
                    ...documentData,
                    updatedAt: new Date().toISOString()
                  })
                : store.add({
                    ...documentData,
                    createdAt: new Date().toISOString()
                  });
                
            request.onsuccess = () => {
                console.log('Документ сохранен успешно с ID:', request.result);
                resolve(request.result);
            };
            
            request.onerror = (event) => {
                console.error('Ошибка сохранения документа:', event.target.error);
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Исключение при сохранении документа:', error);
        throw error;
    }
};

const getDocuments = async () => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['documents'], 'readonly');
        const store = transaction.objectStore('documents');
        
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            
            request.onsuccess = () => {
                resolve(request.result);
            };
            
            request.onerror = () => {
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Исключение при получении документов:', error);
        throw error;
    }
};

const deleteDocument = async (id) => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['documents'], 'readwrite');
        const store = transaction.objectStore('documents');
        
        return new Promise((resolve, reject) => {
            const request = store.delete(id);
            
            request.onsuccess = () => {
                resolve();
            };
            
            request.onerror = () => {
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Исключение при удалении документа:', error);
        throw error;
    }
};