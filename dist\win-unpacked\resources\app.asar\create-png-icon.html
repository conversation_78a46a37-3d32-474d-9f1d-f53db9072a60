<!DOCTYPE html>
<html>
<head>
    <title>Create PNG Icon</title>
</head>
<body>
    <h2>Password Manager Icon Creator</h2>
    <canvas id="canvas" width="256" height="256" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon()">Download Icon as PNG</button>
    <button onclick="downloadICO()">Download as ICO (simulated)</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Создаем иконку
        function drawIcon() {
            // Очищаем canvas
            ctx.clearRect(0, 0, 256, 256);
            
            // Создаем градиентный фон
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#3B82F6');
            gradient.addColorStop(1, '#1E40AF');
            
            // Рисуем круглый фон
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(128, 128, 120, 0, 2 * Math.PI);
            ctx.fill();
            
            // Добавляем обводку
            ctx.strokeStyle = '#1E293B';
            ctx.lineWidth = 4;
            ctx.stroke();
            
            // Рисуем щит
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.beginPath();
            ctx.moveTo(128, 40);
            ctx.lineTo(180, 70);
            ctx.lineTo(180, 140);
            ctx.quadraticCurveTo(180, 180, 128, 200);
            ctx.quadraticCurveTo(76, 180, 76, 140);
            ctx.lineTo(76, 70);
            ctx.closePath();
            ctx.fill();
            
            // Рисуем тело замка
            ctx.fillStyle = '#1E40AF';
            ctx.fillRect(108, 120, 40, 50);
            
            // Скругляем углы замка
            ctx.beginPath();
            ctx.roundRect(108, 120, 40, 50, 4);
            ctx.fill();
            
            // Рисуем дужку замка
            ctx.strokeStyle = '#1E40AF';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(128, 113, 13, Math.PI, 0, false);
            ctx.stroke();
            
            // Рисуем замочную скважину
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(128, 140, 6, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillRect(125, 140, 6, 15);
        }
        
        // Функция для скачивания PNG
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Функция для скачивания как ICO (на самом деле PNG)
        function downloadICO() {
            const link = document.createElement('a');
            link.download = 'icon.ico';
            link.href = canvas.toDataURL('image/png');
            link.click();
            alert('Файл сохранен как PNG. Для настоящего ICO используйте онлайн конвертер.');
        }
        
        // Рисуем иконку при загрузке
        drawIcon();
    </script>
</body>
</html>
