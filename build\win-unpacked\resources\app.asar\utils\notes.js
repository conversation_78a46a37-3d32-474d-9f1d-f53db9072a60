// Функции для работы с заметками
const saveNote = async (noteData) => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['notes'], 'readwrite');
        const store = transaction.objectStore('notes');
        
        return new Promise((resolve, reject) => {
            const request = noteData.id 
                ? store.put({
                    ...noteData,
                    updatedAt: new Date().toISOString()
                  })
                : store.add({
                    ...noteData,
                    createdAt: new Date().toISOString()
                  });
                
            request.onsuccess = () => {
                console.log('Заметка сохранена успешно с ID:', request.result);
                resolve(request.result);
            };
            
            request.onerror = (event) => {
                console.error('Ошибка сохранения заметки:', event.target.error);
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Исключение при сохранении заметки:', error);
        throw error;
    }
};

const getNotes = async () => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['notes'], 'readonly');
        const store = transaction.objectStore('notes');
        
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            
            request.onsuccess = () => {
                resolve(request.result);
            };
            
            request.onerror = () => {
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Исключение при получении заметок:', error);
        throw error;
    }
};

const deleteNote = async (id) => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['notes'], 'readwrite');
        const store = transaction.objectStore('notes');
        
        return new Promise((resolve, reject) => {
            const request = store.delete(id);
            
            request.onsuccess = () => {
                resolve();
            };
            
            request.onerror = () => {
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Исключение при удалении заметки:', error);
        throw error;
    }
};