// Утилиты для работы с локальным хранилищем
const MASTER_PASSWORD_KEY = 'masterPasswordHash';
const FIRST_RUN_KEY = 'passwordManagerFirstRun';

// Сохранение мастер-пароля в localStorage (дублирование для надежности)
const savePasswordToLocalStorage = (hashedPassword) => {
    try {
        localStorage.setItem(MASTER_PASSWORD_KEY, hashedPassword);
        // Устанавливаем флаг, что приложение уже запускалось
        localStorage.setItem(FIRST_RUN_KEY, 'false');
        console.log('Master password saved to localStorage');
        return true;
    } catch (error) {
        console.error('Error saving master password to localStorage:', error);
        return false;
    }
};

// Получение мастер-пароля из localStorage
const getPasswordFromLocalStorage = () => {
    try {
        return localStorage.getItem(MASTER_PASSWORD_KEY);
    } catch (error) {
        console.error('Error getting master password from localStorage:', error);
        return null;
    }
};

// Удаление мастер-пароля из localStorage
const removePasswordFromLocalStorage = () => {
    try {
        localStorage.removeItem(MASTER_PASSWORD_KEY);
        return true;
    } catch (error) {
        console.error('Error removing master password from localStorage:', error);
        return false;
    }
};

// Проверка, является ли это первым запуском приложения
const isFirstRun = () => {
    return localStorage.getItem(FIRST_RUN_KEY) !== 'false';
};