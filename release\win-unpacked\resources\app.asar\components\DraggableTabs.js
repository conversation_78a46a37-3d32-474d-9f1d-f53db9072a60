function DraggableTabs({ tabs, activeTab, onTabClick, onTabsReorder }) {
    const [draggedTab, setDraggedTab] = React.useState(null);
    const [dragOverTab, setDragOverTab] = React.useState(null);
    
    const handleDragStart = (tab) => {
        // Не позволяем перетаскивать заблокированные вкладки
        if (tab.locked || tab.name === 'Банковские карты' || tab.name === 'Заметки') {
            return;
        }
        setDraggedTab(tab);
    };
    
    const handleDragOver = (e, tab) => {
        e.preventDefault();
        if (draggedTab && draggedTab.id !== tab.id) {
            setDragOverTab(tab);
        }
    };
    
    const handleDrop = (e, targetTab) => {
        e.preventDefault();
        
        if (!draggedTab || draggedTab.id === targetTab.id) {
            return;
        }
        
        // Не позволяем перетаскивать вкладки на место заблокированных вкладок
        if (targetTab.locked || targetTab.name === 'Банковские карты' || targetTab.name === 'Заметки') {
            setDraggedTab(null);
            setDragOverTab(null);
            return;
        }
        
        // Создаем новый порядок вкладок
        const reorderedTabs = [...tabs];
        const draggedIndex = tabs.findIndex(t => t.id === draggedTab.id);
        const targetIndex = tabs.findIndex(t => t.id === targetTab.id);
        
        // Удаляем перетаскиваемую вкладку из массива
        reorderedTabs.splice(draggedIndex, 1);
        
        // Вставляем ее на новое место
        reorderedTabs.splice(targetIndex, 0, draggedTab);
        
        // Сохраняем "Банковские карты" и "Заметки" в начале списка
        const cardsTabIndex = reorderedTabs.findIndex(t => t.name === 'Банковские карты');
        if (cardsTabIndex > 0) {
            const cardsTab = reorderedTabs.splice(cardsTabIndex, 1)[0];
            reorderedTabs.unshift(cardsTab);
        }
        
        const notesTabIndex = reorderedTabs.findIndex(t => t.name === 'Заметки');
        if (notesTabIndex > 1) {
            const notesTab = reorderedTabs.splice(notesTabIndex, 1)[0];
            reorderedTabs.splice(1, 0, notesTab);
        }
        
        // Вызываем функцию обратного вызова с новым порядком
        onTabsReorder(reorderedTabs);
        
        // Сбрасываем состояние перетаскивания
        setDraggedTab(null);
        setDragOverTab(null);
    };
    
    const handleDragEnd = () => {
        setDraggedTab(null);
        setDragOverTab(null);
    };
    
    return (
        <div className="flex items-center space-x-2 min-w-max overflow-x-auto">
            {tabs.map(tab => (
                <button
                    key={tab.id}
                    onClick={() => onTabClick(tab.name)}
                    draggable={tab.name !== 'Банковские карты'}
                    onDragStart={() => handleDragStart(tab)}
                    onDragOver={(e) => handleDragOver(e, tab)}
                    onDrop={(e) => handleDrop(e, tab)}
                    onDragEnd={handleDragEnd}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors whitespace-nowrap ${
                        activeTab === tab.name
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                    } ${dragOverTab?.id === tab.id ? 'border-2 border-dashed border-blue-400' : ''}`}
                >
                    {tab.name}
                </button>
            ))}
        </div>
    );
}