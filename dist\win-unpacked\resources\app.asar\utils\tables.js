// Функции для работы с таблицами
const saveTable = async (tableData) => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['tables'], 'readwrite');
        const store = transaction.objectStore('tables');
        
        return new Promise((resolve, reject) => {
            const request = tableData.id 
                ? store.put({
                    ...tableData,
                    updatedAt: new Date().toISOString()
                  })
                : store.add({
                    ...tableData,
                    createdAt: new Date().toISOString()
                  });
                
            request.onsuccess = () => {
                console.log('Таблица сохранена успешно с ID:', request.result);
                resolve(request.result);
            };
            
            request.onerror = (event) => {
                console.error('Ошибка сохранения таблицы:', event.target.error);
                reject(request.error);
            };
        });
    } catch (error) {
        console.error('Исключение в saveTable:', error);
        throw error;
    }
};

const getTables = async () => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['tables'], 'readonly');
        const store = transaction.objectStore('tables');
        
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => {
                const tables = request.result.sort((a, b) => 
                    new Date(b.createdAt || 0) - new Date(a.createdAt || 0)
                );
                resolve(tables);
            };
            request.onerror = () => reject(request.error);
        });
    } catch (error) {
        console.error('Ошибка получения таблиц:', error);
        throw error;
    }
};

const getTable = async (id) => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['tables'], 'readonly');
        const store = transaction.objectStore('tables');
        
        return new Promise((resolve, reject) => {
            const request = store.get(id);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    } catch (error) {
        console.error('Ошибка получения таблицы:', error);
        throw error;
    }
};

const deleteTable = async (id) => {
    try {
        const database = window.db || db;
        if (!database) {
            throw new Error('База данных не инициализирована');
        }
        
        const transaction = database.transaction(['tables'], 'readwrite');
        const store = transaction.objectStore('tables');
        
        return new Promise((resolve, reject) => {
            const request = store.delete(id);
            request.onsuccess = () => {
                console.log('Таблица удалена успешно');
                resolve();
            };
            request.onerror = () => reject(request.error);
        });
    } catch (error) {
        console.error('Ошибка удаления таблицы:', error);
        throw error;
    }
};

// Функции для создания и управления структурой таблиц
const createEmptyTable = (name = 'Новая таблица', rows = 5, cols = 5) => {
    const headers = Array.from({ length: cols }, (_, i) => `Колонка ${i + 1}`);
    const data = Array.from({ length: rows }, () => 
        Array.from({ length: cols }, () => '')
    );
    
    return {
        name,
        headers,
        data,
        createdAt: new Date().toISOString()
    };
};

const addRow = (tableData, index = -1) => {
    const newRow = Array.from({ length: tableData.headers.length }, () => '');
    if (index === -1) {
        tableData.data.push(newRow);
    } else {
        tableData.data.splice(index, 0, newRow);
    }
    return tableData;
};

const addColumn = (tableData, index = -1, headerName = '') => {
    const newHeaderName = headerName || `Колонка ${tableData.headers.length + 1}`;
    
    if (index === -1) {
        tableData.headers.push(newHeaderName);
        tableData.data.forEach(row => row.push(''));
    } else {
        tableData.headers.splice(index, 0, newHeaderName);
        tableData.data.forEach(row => row.splice(index, 0, ''));
    }
    return tableData;
};

const removeRow = (tableData, index) => {
    if (tableData.data.length > 1) {
        tableData.data.splice(index, 1);
    }
    return tableData;
};

const removeColumn = (tableData, index) => {
    if (tableData.headers.length > 1) {
        tableData.headers.splice(index, 1);
        tableData.data.forEach(row => row.splice(index, 1));
    }
    return tableData;
};

const updateCell = (tableData, rowIndex, colIndex, value) => {
    if (tableData.data[rowIndex] && tableData.data[rowIndex][colIndex] !== undefined) {
        tableData.data[rowIndex][colIndex] = value;
    }
    return tableData;
};

const updateHeader = (tableData, index, value) => {
    if (tableData.headers[index] !== undefined) {
        tableData.headers[index] = value;
    }
    return tableData;
};

// Экспорт функций для глобального использования
if (typeof window !== 'undefined') {
    window.saveTable = saveTable;
    window.getTables = getTables;
    window.getTable = getTable;
    window.deleteTable = deleteTable;
    window.createEmptyTable = createEmptyTable;
    window.addRow = addRow;
    window.addColumn = addColumn;
    window.removeRow = removeRow;
    window.removeColumn = removeColumn;
    window.updateCell = updateCell;
    window.updateHeader = updateHeader;
}
