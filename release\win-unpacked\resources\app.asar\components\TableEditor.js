function TableEditor({ table, onSave, onCancel }) {
    const [tableData, setTableData] = React.useState(
        table || createEmptyTable()
    );
    const [tableName, setTableName] = React.useState(tableData.name || 'Новая таблица');
    const [loading, setLoading] = React.useState(false);

    const handleCellChange = (rowIndex, colIndex, value) => {
        const updatedTable = { ...tableData };
        updateCell(updatedTable, rowIndex, colIndex, value);
        setTableData(updatedTable);
    };

    const handleHeaderChange = (index, value) => {
        const updatedTable = { ...tableData };
        updateHeader(updatedTable, index, value);
        setTableData(updatedTable);
    };

    const handleAddRow = (index = -1) => {
        const updatedTable = { ...tableData };
        addRow(updatedTable, index);
        setTableData(updatedTable);
    };

    const handleAddColumn = (index = -1) => {
        const updatedTable = { ...tableData };
        addColumn(updatedTable, index);
        setTableData(updatedTable);
    };

    const handleRemoveRow = (index) => {
        const updatedTable = { ...tableData };
        removeRow(updatedTable, index);
        setTableData(updatedTable);
    };

    const handleRemoveColumn = (index) => {
        const updatedTable = { ...tableData };
        removeColumn(updatedTable, index);
        setTableData(updatedTable);
    };

    const handleSave = async () => {
        if (!tableName.trim()) {
            showToast('Введите название таблицы', 'error');
            return;
        }

        setLoading(true);
        try {
            const tableToSave = {
                ...tableData,
                name: tableName.trim()
            };
            
            await saveTable(tableToSave);
            showToast('Таблица сохранена успешно');
            onSave();
        } catch (error) {
            console.error('Ошибка сохранения таблицы:', error);
            showToast('Ошибка сохранения таблицы', 'error');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-gray-900 z-50">
            <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
                <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-3">
                        <i className="fas fa-table text-2xl text-green-400"></i>
                        <input
                            type="text"
                            value={tableName}
                            onChange={(e) => setTableName(e.target.value)}
                            className="text-xl font-bold bg-transparent border-none outline-none text-white"
                            placeholder="Название таблицы"
                        />
                    </div>
                    <div className="flex items-center space-x-3">
                        <button
                            onClick={handleSave}
                            disabled={loading}
                            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
                        >
                            {loading ? 'Сохранение...' : 'Сохранить'}
                        </button>
                        <button
                            onClick={onCancel}
                            className="text-gray-400 hover:text-white transition-colors"
                            title="Закрыть"
                        >
                            <i className="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </header>

            <div className="p-6 h-full overflow-auto">
                <div className="max-w-full mx-auto">
                    <div className="mb-4 flex space-x-2">
                        <button
                            onClick={() => handleAddRow()}
                            className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
                        >
                            <i className="fas fa-plus mr-2"></i>Добавить строку
                        </button>
                        <button
                            onClick={() => handleAddColumn()}
                            className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
                        >
                            <i className="fas fa-plus mr-2"></i>Добавить колонку
                        </button>
                    </div>

                    <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                            <thead>
                                <tr>
                                    <th className="w-8"></th>
                                    {tableData.headers.map((header, index) => (
                                        <th key={index} className="relative group">
                                            <input
                                                type="text"
                                                value={header}
                                                onChange={(e) => handleHeaderChange(index, e.target.value)}
                                                className="w-full p-2 bg-gray-700 border border-gray-600 text-white text-center font-semibold"
                                                placeholder={`Колонка ${index + 1}`}
                                            />
                                            <div className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                                <button
                                                    onClick={() => handleRemoveColumn(index)}
                                                    className="bg-red-600 hover:bg-red-700 text-white text-xs px-1 py-1 rounded-bl"
                                                    title="Удалить колонку"
                                                >
                                                    <i className="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {tableData.data.map((row, rowIndex) => (
                                    <tr key={rowIndex} className="group">
                                        <td className="relative">
                                            <div className="flex items-center justify-center h-full">
                                                <span className="text-gray-400 text-sm">{rowIndex + 1}</span>
                                                <button
                                                    onClick={() => handleRemoveRow(rowIndex)}
                                                    className="ml-2 opacity-0 group-hover:opacity-100 bg-red-600 hover:bg-red-700 text-white text-xs px-1 py-1 rounded transition-opacity"
                                                    title="Удалить строку"
                                                >
                                                    <i className="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                        {row.map((cell, colIndex) => (
                                            <td key={colIndex}>
                                                <input
                                                    type="text"
                                                    value={cell}
                                                    onChange={(e) => handleCellChange(rowIndex, colIndex, e.target.value)}
                                                    className="w-full p-2 bg-gray-800 border border-gray-600 text-white"
                                                    placeholder="Введите данные"
                                                />
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
}
