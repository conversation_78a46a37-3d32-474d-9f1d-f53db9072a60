// Скрипт для исправления проблемы с сохранением мастер-пароля

// Функция для проверки и исправления состояния хранилища
async function fixPasswordStorage() {
    console.log('Запуск проверки и исправления хранилища паролей...');
    
    // Проверяем наличие пароля в localStorage
    const localStoragePassword = localStorage.getItem('masterPasswordHash');
    
    if (localStoragePassword) {
        console.log('Найден пароль в localStorage, проверяем IndexedDB...');
        
        try {
            // Открываем базу данных
            const dbRequest = indexedDB.open('passwordManagerDB', 5);
            
            dbRequest.onsuccess = async (event) => {
                const db = event.target.result;
                
                // Проверяем наличие пароля в IndexedDB
                const transaction = db.transaction(['settings'], 'readonly');
                const store = transaction.objectStore('settings');
                const request = store.get('masterPassword');
                
                request.onsuccess = () => {
                    if (!request.result || !request.result.value) {
                        console.log('Пароль не найден в IndexedDB, восстанавливаем из localStorage...');
                        
                        // Сохраняем пароль из localStorage в IndexedDB
                        const writeTransaction = db.transaction(['settings'], 'readwrite');
                        const writeStore = writeTransaction.objectStore('settings');
                        const writeRequest = writeStore.put({ 
                            key: 'masterPassword', 
                            value: localStoragePassword 
                        });
                        
                        writeRequest.onsuccess = () => {
                            console.log('Пароль успешно восстановлен в IndexedDB');
                            showToast('Данные восстановлены');
                        };
                        
                        writeRequest.onerror = (error) => {
                            console.error('Ошибка при восстановлении пароля в IndexedDB:', error);
                        };
                    } else {
                        console.log('Пароль найден в IndexedDB:', !!request.result.value);
                    }
                };
                
                request.onerror = (error) => {
                    console.error('Ошибка при проверке пароля в IndexedDB:', error);
                };
            };
            
            dbRequest.onerror = (error) => {
                console.error('Ошибка при открытии IndexedDB:', error);
            };
            
        } catch (error) {
            console.error('Исключение при исправлении хранилища паролей:', error);
        }
    } else {
        console.log('Пароль не найден в localStorage');
    }
}

// Запускаем исправление при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    // Даем время для инициализации базы данных
    setTimeout(fixPasswordStorage, 1000);
});