function NoteView({ note, onDelete, onEdit }) {
    try {
        const handleDelete = async () => {
            if (confirm('Вы уверены, что хотите удалить эту заметку?')) {
                try {
                    await onDelete(note.id);
                } catch (error) {
                    console.error('Delete error:', error);
                    showToast('Не удалось удалить заметку', 'error');
                }
            }
        };
        
        return (
            <div className="glass-effect rounded-xl p-6 card-hover">
                <div className="flex justify-between items-start mb-4">
                    <h3 
                        className="text-xl font-semibold" 
                        style={{ 
                            color: note.titleColor || note.color || '#FFFFFF', 
                            fontFamily: note.titleFont || note.font || 'sans-serif' 
                        }}
                    >
                        {note.title || 'Без заголовка'}
                    </h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={() => onEdit(note)}
                            className="text-blue-400 hover:text-blue-300 transition-colors"
                            type="button"
                        >
                            <i className="fas fa-edit"></i>
                        </button>
                        <button
                            onClick={handleDelete}
                            className="text-red-400 hover:text-red-300 transition-colors"
                            type="button"
                        >
                            <i className="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div className="space-y-4">
                    {note.content && (
                        <p 
                            className="whitespace-pre-wrap text-base" 
                            style={{ 
                                color: note.contentColor || note.color || '#FFFFFF', 
                                fontFamily: note.contentFont || note.font || 'sans-serif' 
                            }}
                        >
                            {note.content}
                        </p>
                    )}
                    
                    {note.images && note.images.length > 0 && (
                        <div className="grid grid-cols-2 gap-3 mt-4">
                            {note.images.map((image, index) => (
                                <div key={index} className="aspect-square">
                                    <img 
                                        src={image} 
                                        alt={`Изображение ${index + 1}`}
                                        className="rounded-lg w-full h-full object-cover"
                                    />
                                </div>
                            ))}
                        </div>
                    )}
                    
                    <div className="text-xs text-gray-500 mt-3">
                        {note.createdAt && (
                            <span>
                                {new Date(note.createdAt).toLocaleDateString()} {new Date(note.createdAt).toLocaleTimeString()}
                            </span>
                        )}
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('NoteView component error:', error);
        reportError(error);
    }
}