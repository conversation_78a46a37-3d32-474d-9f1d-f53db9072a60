function LoginForm({ onLogin, isFirstTime, onResetPassword }) {
    try {
        const [password, setPassword] = React.useState('');
        const [confirmPassword, setConfirmPassword] = React.useState('');
        const [loading, setLoading] = React.useState(false);
        const [showPassword, setShowPassword] = React.useState(false);

        const handleSubmit = async (e) => {
            e.preventDefault();
            setLoading(true);

            try {
                if (isFirstTime) {
                    if (password !== confirmPassword) {
                        showToast('Пароли не совпадают', 'error');
                        return;
                    }
                    if (password.length < 6) {
                        showToast('Пароль должен содержать не менее 6 символов', 'error');
                        return;
                    }
                    const hashedPassword = await hashPassword(password);
                    
                    // Сохраняем пароль в IndexedDB и localStorage
                    await saveMasterPassword(hashedPassword);
                    
                    // Дополнительно сохраняем в localStorage напрямую
                    savePasswordToLocalStorage(hashedPassword);
                    
                    showToast('Мастер-пароль успешно создан');
                    onLogin();
                } else {
                    const hashedPassword = await hashPassword(password);
                    const storedPassword = await getMasterPassword();
                    console.log('Comparing passwords:', { hashedPassword, storedPassword });
                    if (storedPassword && hashedPassword === storedPassword) {
                        onLogin();
                    } else {
                        showToast('Неверный пароль', 'error');
                    }
                }
            } catch (error) {
                showToast('Произошла ошибка', 'error');
            } finally {
                setLoading(false);
            }
        };

        return (
            <div className="min-h-screen flex items-center justify-center p-4" data-name="login-container" data-file="components/LoginForm.js">
                <div className="glass-effect rounded-2xl p-8 w-full max-w-md">
                    <div className="text-center mb-8">
                        <i className="fas fa-shield-alt text-4xl text-blue-400 mb-4"></i>
                        <h1 className="text-2xl font-bold mb-2">Password Manager</h1>
                        <p className="text-gray-400">
                            {isFirstTime ? 'Создайте мастер-пароль' : 'Введите мастер-пароль'}
                        </p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="relative">
                            <input
                                type={showPassword ? "text" : "password"}
                                placeholder="Мастер-пароль"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                required
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                            >
                                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                            </button>
                        </div>

                        {isFirstTime && (
                            <div className="relative">
                                <input
                                    type={showPassword ? "text" : "password"}
                                    placeholder="Подтвердите пароль"
                                    value={confirmPassword}
                                    onChange={(e) => setConfirmPassword(e.target.value)}
                                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                    required
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                                >
                                    <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                                </button>
                            </div>
                        )}

                        <button
                            type="submit"
                            disabled={loading}
                            className="w-full py-3 btn-primary rounded-lg font-semibold disabled:opacity-50"
                        >
                            {loading ? 'Обработка...' : (isFirstTime ? 'Создать пароль' : 'Войти')}
                        </button>
                        
                        {/* Ссылки на сброс пароля и удаление данных убраны */}
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('LoginForm component error:', error);
        reportError(error);
    }
}
