function CardView({ card, onDelete }) {
    // Проверяем, что card определен
    if (!card) {
        console.error('CardView received undefined card');
        return null;
    }
    try {
        // По умолчанию номер карты скрыт (false)
        // Инициализируем состояние из localStorage, если доступно
        const [showCardNumber, setShowCardNumber] = React.useState(() => {
            if (card && card.id && typeof localStorage !== 'undefined') {
                const saved = localStorage.getItem(`card_${card.id}_showNumber`);
                return saved === 'true';
            }
            return false;
        });
        // Инициализируем состояние из localStorage, если доступно
        const [showCVV, setShowCVV] = React.useState(() => {
            if (card && card.id && typeof localStorage !== 'undefined') {
                const saved = localStorage.getItem(`card_${card.id}_showCVV`);
                return saved === 'true';
            }
            return false;
        });
        
        // Используем useEffect для отладки состояния и сохранения в локальное хранилище
        React.useEffect(() => {
            console.log('CardView rendered, showCardNumber:', showCardNumber);
            // Сохраняем состояние в локальное хранилище для конкретной карты
            if (card && card.id && typeof localStorage !== 'undefined') {
                localStorage.setItem(`card_${card.id}_showNumber`, showCardNumber ? 'true' : 'false');
            }
        }, [showCardNumber, card]);
        
        // Сохраняем состояние видимости CVV
        React.useEffect(() => {
            if (card && card.id && typeof localStorage !== 'undefined') {
                localStorage.setItem(`card_${card.id}_showCVV`, showCVV ? 'true' : 'false');
            }
        }, [showCVV, card]);

        const handleCopy = async (text, field) => {
            try {
                const success = await copyToClipboard(text);
                if (success) {
                    showToast(`${field} copied to clipboard`);
                }
            } catch (error) {
                console.error('Copy error:', error);
                showToast('Failed to copy to clipboard', 'error');
            }
        };

        const handleDelete = async () => {
            if (confirm('Are you sure you want to delete this card?')) {
                try {
                    await onDelete(card.id);
                } catch (error) {
                    console.error('Delete card error:', error);
                    showToast('Failed to delete card', 'error');
                }
            }
        };

        const maskCardNumber = (number) => {
            if (!number) return '•••• •••• •••• ••••';
            
            // Проверяем, что номер карты - строка
            const cardNumber = String(number);
            
            // Удаляем все пробелы
            const cleaned = cardNumber.replace(/\s+/g, '');
            
            // Форматируем по 4 цифры и маскируем все, кроме последних 4
            const formatted = cleaned.replace(/(.{4})/g, '$1 ').trim();
            return formatted.replace(/\d(?=.{5})/g, '*');
        };

        return (
            <div className="glass-effect rounded-xl p-6 card-hover" data-name="card-view" data-file="components/CardView.js">
                <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold text-white">{card.title}</h3>
                    <button
                        onClick={handleDelete}
                        className="text-red-400 hover:text-red-300 transition-colors"
                        type="button"
                    >
                        <i className="fas fa-trash"></i>
                    </button>
                </div>

                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Card Number</label>
                            <p className="text-white font-mono">
                                {showCardNumber ? (card.cardNumber || '') : maskCardNumber(card.cardNumber)}
                            </p>
                        </div>
                        <div className="flex space-x-2">
                            <button
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    const newValue = !showCardNumber;
                                    console.log('Toggle card number visibility to:', newValue);
                                    setShowCardNumber(newValue);
                                }}
                                className="text-gray-400 hover:text-gray-300"
                                type="button"
                            >
                                <i className={`fas ${showCardNumber ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                            </button>
                            <button
                                onClick={() => handleCopy(card.cardNumber, 'Card Number')}
                                className="text-blue-400 hover:text-blue-300"
                                type="button"
                            >
                                <i className="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">CVV</label>
                            <p className="text-white font-mono">
                                {showCVV ? (card.cvv || '') : '•••'}
                            </p>
                        </div>
                        <div className="flex space-x-2">
                            <button
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setShowCVV(!showCVV);
                                }}
                                className="text-gray-400 hover:text-gray-300"
                                type="button"
                            >
                                <i className={`fas ${showCVV ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                            </button>
                            <button
                                onClick={() => handleCopy(card.cvv, 'CVV')}
                                className="text-blue-400 hover:text-blue-300"
                                type="button"
                            >
                                <i className="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Expiry Date (MM/YYYY)</label>
                            <p className="text-white">{card.expiryMonth}/{card.expiryYear}</p>
                        </div>
                        <button
                            onClick={() => handleCopy(`${card.expiryMonth}/${card.expiryYear}`, 'Expiry Date')}
                            className="text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <label className="text-sm text-gray-400">Cardholder Name</label>
                            <p className="text-white">{card.firstName} {card.lastName}</p>
                        </div>
                        <button
                            onClick={() => handleCopy(`${card.firstName} ${card.lastName}`, 'Cardholder Name')}
                            className="text-blue-400 hover:text-blue-300"
                            type="button"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                    </div>

                    {card.notes && (
                        <div>
                            <label className="text-sm text-gray-400">Notes</label>
                            <p className="text-white text-sm">{card.notes}</p>
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('CardView component error:', error);
        reportError(error);
    }
}
