function AddPasswordForm({ onAdd, onCancel, tabs, editPassword = null }) {
    try {
        const initialFormData = editPassword ? {
            ...editPassword,
            titleFont: editPassword.titleFont || 'sans-serif',
            usernameFont: editPassword.usernameFont || 'sans-serif',
            passwordFont: editPassword.passwordFont || 'monospace',
            urlFont: editPassword.urlFont || 'sans-serif',
            phoneFont: editPassword.phoneFont || 'sans-serif',
            notesFont: editPassword.notesFont || 'sans-serif',
            titleColor: editPassword.titleColor || '#3B82F6',
            usernameColor: editPassword.usernameColor || '#3B82F6',
            passwordColor: editPassword.passwordColor || '#3B82F6',
            urlColor: editPassword.urlColor || '#3B82F6',
            phoneColor: editPassword.phoneColor || '#3B82F6',
            notesColor: editPassword.notesColor || '#3B82F6',
            emailColor: editPassword.emailColor || '#3B82F6',
            emailFont: editPassword.emailFont || 'sans-serif',
            imageUrl: editPassword.imageUrl || '',
            cardBgColor: editPassword.cardBgColor || 'transparent'
        } : {
            title: '',
            email: '',
            username: '',
            password: '',
            notes: '',
            category: 'Соцсети',
            color: '#3B82F6',
            url: '',
            phone: '',
            titleFont: 'sans-serif',
            usernameFont: 'sans-serif',
            passwordFont: 'monospace',
            urlFont: 'sans-serif',
            phoneFont: 'sans-serif',
            notesFont: 'sans-serif',
            titleColor: '#3B82F6',
            usernameColor: '#3B82F6',
            passwordColor: '#3B82F6',
            urlColor: '#3B82F6',
            phoneColor: '#3B82F6',
            notesColor: '#3B82F6',
            emailColor: '#3B82F6',
            emailFont: 'sans-serif',
            imageUrl: '',
            cardBgColor: 'transparent'
        };

        const [formData, setFormData] = React.useState(initialFormData);
        const [showPassword, setShowPassword] = React.useState(false);

        const colorOptions = [
            { name: 'Blue', value: '#3B82F6' },
            { name: 'Green', value: '#10B981' },
            { name: 'Purple', value: '#8B5CF6' },
            { name: 'Red', value: '#EF4444' },
            { name: 'Yellow', value: '#F59E0B' },
            { name: 'Pink', value: '#EC4899' },
            { name: 'White', value: '#FFFFFF' },
            { name: 'Lime', value: '#84CC16' },
            { name: 'Orange', value: '#F97316' },
            { name: 'Teal', value: '#14B8A6' },
            { name: 'Cyan', value: '#06B6D4' },
            { name: 'Indigo', value: '#6366F1' },
            { name: 'Amber', value: '#F59E0B' },
            { name: 'Gray', value: '#9CA3AF' }
        ];
        
        const bgColorOptions = [
            { name: 'Transparent', value: 'transparent' },
            { name: 'Dark Blue', value: 'rgba(30, 58, 138, 0.4)' },
            { name: 'Dark Green', value: 'rgba(6, 78, 59, 0.4)' },
            { name: 'Dark Purple', value: 'rgba(76, 29, 149, 0.4)' },
            { name: 'Dark Red', value: 'rgba(127, 29, 29, 0.4)' },
            { name: 'Dark Yellow', value: 'rgba(120, 53, 15, 0.4)' },
            { name: 'Dark Pink', value: 'rgba(131, 24, 67, 0.4)' },
            { name: 'Dark Gray', value: 'rgba(55, 65, 81, 0.4)' },
            { name: 'Light Blue', value: 'rgba(59, 130, 246, 0.15)' },
            { name: 'Light Green', value: 'rgba(16, 185, 129, 0.15)' },
            { name: 'Light Purple', value: 'rgba(139, 92, 246, 0.15)' },
            { name: 'Light Red', value: 'rgba(239, 68, 68, 0.15)' },
            { name: 'Light Yellow', value: 'rgba(245, 158, 11, 0.15)' },
            { name: 'Light Pink', value: 'rgba(236, 72, 153, 0.15)' }
        ];
        
        const fontOptions = [
            { name: 'Monospace', value: 'monospace' },
            { name: 'Sans-serif', value: 'sans-serif' },
            { name: 'Serif', value: 'serif' },
            { name: 'Cursive', value: 'cursive' },
            { name: 'Fantasy', value: 'fantasy' },
            { name: 'Arial', value: 'Arial, sans-serif' },
            { name: 'Verdana', value: 'Verdana, sans-serif' },
            { name: 'Tahoma', value: 'Tahoma, sans-serif' },
            { name: 'Trebuchet MS', value: 'Trebuchet MS, sans-serif' },
            { name: 'Georgia', value: 'Georgia, serif' },
            { name: 'Courier New', value: 'Courier New, monospace' },
            { name: 'Brush Script', value: 'Brush Script MT, cursive' },
            { name: 'Impact', value: 'Impact, fantasy' }
        ];

        const handleSubmit = async (e) => {
            e.preventDefault();
            
            // Проверяем обязательные поля
            if (!formData.username || !formData.password) {
                showToast('Please fill in all required fields marked with *', 'error');
                return;
            }

            try {
                console.log('Submitting form data:', formData);
                const result = await savePassword(formData);
                console.log('Save result:', result);
                showToast('Password saved successfully');
                onAdd();
            } catch (error) {
                console.error('Save password error:', error);
                showToast('Failed to save password: ' + (error.message || 'Unknown error'), 'error');
            }
        };

        const handleGeneratePassword = () => {
            const generated = generatePassword(16);
            setFormData({ ...formData, password: generated });
        };
        
        const handleImageUpload = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onloadend = () => {
                    setFormData({ ...formData, imageUrl: reader.result });
                };
                reader.readAsDataURL(file);
            }
        };

        const categoryTabs = tabs.filter(tab => tab.name !== 'Все' && tab.name !== 'Банковские карты');

        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" data-name="add-form-modal" data-file="components/AddPasswordForm.js">
                <div className="glass-effect rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-bold">{editPassword ? 'Edit Password' : 'Add New Password'}</h2>
                        <button onClick={onCancel} className="text-gray-400 hover:text-white">
                            <i className="fas fa-times"></i>
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-1">
                            <div className="flex justify-between">
                                <label className="text-xs text-gray-400">Title</label>
                                <div className="flex items-center space-x-2">
                                    <div className="flex space-x-1">
                                        {colorOptions.map(color => (
                                            <button
                                                key={color.value}
                                                type="button"
                                                onClick={() => setFormData({ ...formData, titleColor: color.value })}
                                                className={`w-4 h-4 rounded-full border transition-all ${
                                                    formData.titleColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                                }`}
                                                style={{ backgroundColor: color.value }}
                                            ></button>
                                        ))}
                                    </div>
                                    <select 
                                        value={formData.titleFont} 
                                        onChange={(e) => setFormData({ ...formData, titleFont: e.target.value })}
                                        className="text-xs bg-gray-800 border-none text-gray-400"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <input
                                type="text"
                                placeholder="Title"
                                value={formData.title}
                                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{ color: formData.titleColor, fontFamily: formData.titleFont }}
                            />
                        </div>

                        <div className="space-y-1">
                            <label className="text-xs text-gray-400">Card Background Color</label>
                            <div className="flex flex-wrap gap-2">
                                {bgColorOptions.map(color => (
                                    <button
                                        key={color.value}
                                        type="button"
                                        onClick={() => setFormData({ ...formData, cardBgColor: color.value })}
                                        className={`w-8 h-8 rounded border transition-all ${
                                            formData.cardBgColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                        }`}
                                        style={{ 
                                            backgroundColor: color.value,
                                            position: 'relative'
                                        }}
                                    >
                                        {formData.cardBgColor === color.value && (
                                            <i className="fas fa-check absolute inset-0 flex items-center justify-center text-white"></i>
                                        )}
                                    </button>
                                ))}
                            </div>
                            <div 
                                className="mt-2 p-3 rounded-lg border border-gray-700"
                                style={{ backgroundColor: formData.cardBgColor }}
                            >
                                <p className="text-xs text-center">Preview of card background</p>
                            </div>
                        </div>

                        <div className="space-y-1">
                            <label className="text-xs text-gray-400">Image (Optional)</label>
                            <div className="flex items-center space-x-3">
                                <input
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageUpload}
                                    className="hidden"
                                    id="image-upload"
                                />
                                <label 
                                    htmlFor="image-upload" 
                                    className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg cursor-pointer flex items-center"
                                >
                                    <i className="fas fa-upload mr-2"></i>
                                    Upload Image
                                </label>
                                {formData.imageUrl && (
                                    <div className="relative">
                                        <img 
                                            src={formData.imageUrl} 
                                            alt="Preview" 
                                            className="h-16 w-16 object-cover rounded-lg"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setFormData({ ...formData, imageUrl: '' })}
                                            className="absolute -top-2 -right-2 bg-red-500 rounded-full w-5 h-5 flex items-center justify-center text-xs"
                                        >
                                            <i className="fas fa-times"></i>
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="space-y-1">
                            <div className="flex justify-between">
                                <label className="text-xs text-gray-400">Website URL</label>
                                <div className="flex items-center space-x-2">
                                    <select 
                                        value={formData.urlFont} 
                                        onChange={(e) => setFormData({ ...formData, urlFont: e.target.value })}
                                        className="text-xs bg-gray-800 border-none text-gray-400"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <input
                                type="url"
                                placeholder="Website URL"
                                value={formData.url}
                                onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{ color: '#FFFFFF', fontFamily: formData.urlFont }}
                            />
                        </div>

                        <div className="space-y-1">
                            <div className="flex justify-between">
                                <label className="text-xs text-gray-400">Category *</label>
                                <div className="flex space-x-1">
                                    {colorOptions.map(color => (
                                        <button
                                            key={color.value}
                                            type="button"
                                            onClick={() => setFormData({ ...formData, color: color.value })}
                                            className={`w-4 h-4 rounded-full border transition-all ${
                                                formData.color === color.value ? 'border-white scale-110' : 'border-gray-600'
                                            }`}
                                            style={{ backgroundColor: color.value }}
                                        ></button>
                                    ))}
                                </div>
                            </div>
                            <select
                                value={formData.category}
                                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{ color: formData.color }}
                                required
                            >
                                {categoryTabs.map(tab => (
                                    <option key={tab.id} value={tab.name}>{tab.name}</option>
                                ))}
                            </select>
                        </div>

                        <div className="space-y-1">
                            <div className="flex justify-between">
                                <label className="text-xs text-gray-400">Email</label>
                                <div className="flex items-center space-x-2">
                                    <div className="flex space-x-1">
                                        {colorOptions.map(color => (
                                            <button
                                                key={color.value}
                                                type="button"
                                                onClick={() => setFormData({ ...formData, emailColor: color.value })}
                                                className={`w-4 h-4 rounded-full border transition-all ${
                                                    formData.emailColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                                }`}
                                                style={{ backgroundColor: color.value }}
                                            ></button>
                                        ))}
                                    </div>
                                    <select 
                                        value={formData.emailFont || 'sans-serif'} 
                                        onChange={(e) => setFormData({ ...formData, emailFont: e.target.value })}
                                        className="text-xs bg-gray-800 border-none text-gray-400"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <input
                                type="email"
                                placeholder="Email"
                                value={formData.email}
                                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{ color: formData.emailColor, fontFamily: formData.emailFont || 'sans-serif' }}
                            />
                        </div>

                        <div className="space-y-1">
                            <div className="flex justify-between">
                                <label className="text-xs text-gray-400">Username *</label>
                                <div className="flex items-center space-x-2">
                                    <div className="flex space-x-1">
                                        {colorOptions.map(color => (
                                            <button
                                                key={color.value}
                                                type="button"
                                                onClick={() => setFormData({ ...formData, usernameColor: color.value })}
                                                className={`w-4 h-4 rounded-full border transition-all ${
                                                    formData.usernameColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                                }`}
                                                style={{ backgroundColor: color.value }}
                                            ></button>
                                        ))}
                                    </div>
                                    <select 
                                        value={formData.usernameFont} 
                                        onChange={(e) => setFormData({ ...formData, usernameFont: e.target.value })}
                                        className="text-xs bg-gray-800 border-none text-gray-400"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <input
                                type="text"
                                placeholder="Username *"
                                value={formData.username}
                                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{ color: formData.usernameColor, fontFamily: formData.usernameFont }}
                                required
                            />
                        </div>

                        <div className="space-y-1">
                            <div className="flex justify-between">
                                <label className="text-xs text-gray-400">Password *</label>
                                <div className="flex items-center space-x-2">
                                    <div className="flex space-x-1">
                                        {colorOptions.map(color => (
                                            <button
                                                key={color.value}
                                                type="button"
                                                onClick={() => setFormData({ ...formData, passwordColor: color.value })}
                                                className={`w-4 h-4 rounded-full border transition-all ${
                                                    formData.passwordColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                                }`}
                                                style={{ backgroundColor: color.value }}
                                            ></button>
                                        ))}
                                    </div>
                                    <select 
                                        value={formData.passwordFont} 
                                        onChange={(e) => setFormData({ ...formData, passwordFont: e.target.value })}
                                        className="text-xs bg-gray-800 border-none text-gray-400"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <div className="relative">
                                <input
                                    type={showPassword ? "text" : "password"}
                                    placeholder="Password *"
                                    value={formData.password}
                                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                                    className="w-full px-4 py-3 pr-20 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                    style={{ color: formData.passwordColor, fontFamily: formData.passwordFont }}
                                    required
                                />
                                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-1">
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="text-gray-400 hover:text-gray-300"
                                    >
                                        <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                                    </button>
                                    <button
                                        type="button"
                                        onClick={handleGeneratePassword}
                                        className="text-blue-400 hover:text-blue-300"
                                    >
                                        <i className="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-1">
                            <div className="flex justify-between">
                                <label className="text-xs text-gray-400">Phone Number</label>
                                <div className="flex items-center space-x-2">
                                    <div className="flex space-x-1">
                                        {colorOptions.map(color => (
                                            <button
                                                key={color.value}
                                                type="button"
                                                onClick={() => setFormData({ ...formData, phoneColor: color.value })}
                                                className={`w-4 h-4 rounded-full border transition-all ${
                                                    formData.phoneColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                                }`}
                                                style={{ backgroundColor: color.value }}
                                            ></button>
                                        ))}
                                    </div>
                                    <select 
                                        value={formData.phoneFont} 
                                        onChange={(e) => setFormData({ ...formData, phoneFont: e.target.value })}
                                        className="text-xs bg-gray-800 border-none text-gray-400"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <input
                                type="tel"
                                placeholder="Phone Number"
                                value={formData.phone}
                                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{ color: formData.phoneColor, fontFamily: formData.phoneFont }}
                            />
                        </div>

                        <div className="space-y-1">
                            <div className="flex justify-between">
                                <label className="text-xs text-gray-400">Additional Notes</label>
                                <div className="flex items-center space-x-2">
                                    <div className="flex space-x-1">
                                        {colorOptions.map(color => (
                                            <button
                                                key={color.value}
                                                type="button"
                                                onClick={() => setFormData({ ...formData, notesColor: color.value })}
                                                className={`w-4 h-4 rounded-full border transition-all ${
                                                    formData.notesColor === color.value ? 'border-white scale-110' : 'border-gray-600'
                                                }`}
                                                style={{ backgroundColor: color.value }}
                                            ></button>
                                        ))}
                                    </div>
                                    <select 
                                        value={formData.notesFont} 
                                        onChange={(e) => setFormData({ ...formData, notesFont: e.target.value })}
                                        className="text-xs bg-gray-800 border-none text-gray-400"
                                    >
                                        {fontOptions.map(font => (
                                            <option key={font.value} value={font.value}>{font.name}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            <textarea
                                placeholder="Additional Notes (Optional)"
                                value={formData.notes}
                                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none resize-none"
                                style={{ color: formData.notesColor, fontFamily: formData.notesFont }}
                                rows="3"
                            ></textarea>
                        </div>

                        <div className="flex space-x-3 pt-4">
                            <button
                                type="submit"
                                className="flex-1 py-3 btn-primary rounded-lg font-semibold"
                            >
                                {editPassword ? 'Update Password' : 'Save Password'}
                            </button>
                            <button
                                type="button"
                                onClick={onCancel}
                                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('AddPasswordForm component error:', error);
        reportError(error);
    }
}