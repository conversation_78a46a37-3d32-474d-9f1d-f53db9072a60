@echo off
echo ========================================
echo Password Manager - Installation Script
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Download the LTS version and install it.
    echo.
    echo After installation, restart this script.
    echo.
    pause
    exit /b 1
)

echo Node.js found: 
node --version

echo.
echo Checking npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available!
    pause
    exit /b 1
)

echo npm version: 
npm --version

echo.
echo Installing Electron and dependencies...
echo This may take a few minutes...
echo.

call npm install

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install dependencies!
    echo.
    echo Try the following solutions:
    echo 1. Run as Administrator
    echo 2. Clear npm cache: npm cache clean --force
    echo 3. Delete node_modules folder and try again
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo You can now:
echo 1. Run 'run-dev.bat' to start in development mode
echo 2. Run 'build.bat' to create executable files
echo.
echo For more information, see README.md
echo.
pause
