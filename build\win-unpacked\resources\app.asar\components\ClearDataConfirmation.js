function ClearDataConfirmation({ onConfirm, onCancel }) {
    const [password, setPassword] = React.useState('');
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState('');
    const [showPassword, setShowPassword] = React.useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            // Хешируем введенный пароль
            const hashedPassword = await hashPassword(password);
            
            // Получаем сохраненный мастер-пароль
            const storedPassword = await getMasterPassword();
            
            // Проверяем совпадение паролей
            if (storedPassword && hashedPassword === storedPassword) {
                // Пароль верный, выполняем очистку
                onConfirm();
            } else {
                // Пароль неверный
                setError('Неверный пароль');
            }
        } catch (error) {
            console.error('Error validating password:', error);
            setError('Произошла ошибка при проверке пароля');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-gray-900 flex items-start justify-center pt-32 p-4 z-[100]">
            <div className="bg-gray-800 border border-gray-700 rounded-2xl p-6 w-full max-w-md">
                <div className="text-center mb-6">
                    <i className="fas fa-exclamation-triangle text-4xl text-yellow-400 mb-4"></i>
                    <h2 className="text-xl font-bold">Подтверждение очистки данных</h2>
                    <p className="text-gray-400 mt-2">
                        Для очистки всех данных введите ваш мастер-пароль
                    </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="relative">
                        <input
                            type={showPassword ? "text" : "password"}
                            placeholder="Введите мастер-пароль"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                            required
                        />
                        <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                        >
                            <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                        </button>
                    </div>

                    {error && (
                        <div className="text-red-400 text-sm">
                            {error}
                        </div>
                    )}

                    <div className="flex space-x-3 pt-2">
                        <button
                            type="submit"
                            disabled={loading}
                            className="flex-1 py-3 bg-red-600 hover:bg-red-700 rounded-lg font-semibold transition-colors disabled:opacity-50"
                        >
                            {loading ? 'Проверка...' : 'Подтвердить'}
                        </button>
                        <button
                            type="button"
                            onClick={onCancel}
                            className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors"
                        >
                            Отмена
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}