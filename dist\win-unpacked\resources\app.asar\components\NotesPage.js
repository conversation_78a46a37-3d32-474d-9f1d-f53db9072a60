function NotesPage({ onClose }) {
    const [notes, setNotes] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [showNoteForm, setShowNoteForm] = React.useState(false);
    const [editingNote, setEditingNote] = React.useState(null);

    React.useEffect(() => {
        loadNotes();
    }, []);

    const loadNotes = async () => {
        try {
            setLoading(true);
            const savedNotes = await getNotes();
            setNotes(savedNotes || []);
        } catch (error) {
            console.error('Ошибка при загрузке заметок:', error);
            showToast('Не удалось загрузить заметки', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleAddNote = () => {
        setShowNoteForm(false);
        setEditingNote(null);
        loadNotes();
    };

    const handleEditNote = (note) => {
        setEditingNote(note);
        setShowNoteForm(true);
    };

    const handleDeleteNote = async (id) => {
        try {
            await deleteNote(id);
            showToast('Заметка удалена успешно');
            await loadNotes();
            return true;
        } catch (error) {
            console.error('Delete note error:', error);
            showToast('Не удалось удалить заметку', 'error');
            throw error;
        }
    };

    return (
        <div className="fixed inset-0 bg-gray-900 z-50">
            <div className="w-full h-full flex flex-col">
                <div className="glass-effect border-b border-gray-700 px-6 py-4">
                    <div className="flex justify-between items-center">
                        <h2 className="text-2xl font-bold">Заметки</h2>
                        <div className="flex space-x-3">
                            <button
                                onClick={() => setShowNoteForm(true)}
                                className="text-blue-400 hover:text-blue-300 px-3 py-1 rounded-lg border border-blue-400 hover:border-blue-300 transition-colors"
                                title="Добавить заметку"
                            >
                                <i className="fas fa-plus mr-2"></i>
                                Новая заметка
                            </button>
                            <button 
                                onClick={onClose} 
                                className="text-gray-400 hover:text-white"
                            >
                                <i className="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div className="flex-1 overflow-auto p-6">
                    {loading ? (
                        <div className="text-center py-12">
                            <i className="fas fa-spinner fa-spin text-2xl text-blue-400"></i>
                            <p className="mt-4 text-gray-400">Загрузка заметок...</p>
                        </div>
                    ) : notes.length === 0 ? (
                        <div className="text-center py-12">
                            <i className="fas fa-sticky-note text-5xl text-gray-600 mb-4"></i>
                            <p className="text-gray-400 text-lg mb-4">У вас пока нет заметок</p>
                            <button
                                onClick={() => setShowNoteForm(true)}
                                className="px-6 py-3 btn-primary rounded-lg font-semibold"
                            >
                                Создать заметку
                            </button>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
                            {notes.map(note => (
                                <NoteView
                                    key={note.id}
                                    note={note}
                                    onDelete={handleDeleteNote}
                                    onEdit={handleEditNote}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {showNoteForm && (
                <NoteForm
                    onAdd={handleAddNote}
                    onCancel={() => {
                        setShowNoteForm(false);
                        setEditingNote(null);
                    }}
                    editNote={editingNote}
                />
            )}
        </div>
    );
}