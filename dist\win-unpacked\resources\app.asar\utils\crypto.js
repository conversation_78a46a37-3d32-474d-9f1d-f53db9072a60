// Cryptography utilities
const hashPassword = async (password) => {
    try {
        if (!password) {
            console.error('Empty password provided to hashPassword');
            throw new Error('Password cannot be empty');
        }
        
        const encoder = new TextEncoder();
        const data = encoder.encode(password);
        const hash = await crypto.subtle.digest('SHA-256', data);
        const hashString = Array.from(new Uint8Array(hash))
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
            
        console.log('Password hashed successfully');
        return hashString;
    } catch (error) {
        console.error('Error hashing password:', error);
        throw error;
    }
};

const generatePassword = (length = 12) => {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
};

const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        return true;
    }
};

// Определение функции reportError, если она не существует
if (typeof reportError !== 'function') {
    window.reportError = function(err) {
        console.error('Error reported:', err);
    };
}

// Функция showToast перемещена в отдельный файл notifications.js
