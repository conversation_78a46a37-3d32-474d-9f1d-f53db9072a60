@echo off
echo ========================================
echo Password Manager - Fresh Build Script
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found: 
node --version

echo.
echo Creating icon...
node create-simple-icon.js

echo.
echo Building application with fresh output directory...
echo.

echo Building portable version...
call npx electron-builder --win --x64 --config.win.target=portable --config.directories.output=build

if %errorlevel% neq 0 (
    echo ERROR: Failed to build portable version
    echo Trying with different approach...
    
    echo.
    echo Building unpacked version...
    call npx electron-builder --dir --config.directories.output=build
    
    if %errorlevel% neq 0 (
        echo ERROR: All build methods failed
        pause
        exit /b 1
    ) else (
        echo Unpacked version created successfully!
        echo You can run the application from: build\win-unpacked\Password Manager.exe
    )
) else (
    echo Portable version created successfully!
)

echo.
echo Building installer...
call npx electron-builder --win --x64 --config.win.target=nsis --config.directories.output=build

if %errorlevel% neq 0 (
    echo Warning: Failed to build installer
    echo Portable version should still be available
)

echo.
echo ========================================
echo Build process completed!
echo ========================================
echo.

if exist "build" (
    echo Files created in 'build' folder:
    dir /b "build\*.exe" 2>nul
    if %errorlevel% neq 0 (
        echo No exe files found, checking for unpacked version...
        if exist "build\win-unpacked" (
            echo Unpacked version available in: build\win-unpacked\
            if exist "build\win-unpacked\Password Manager.exe" (
                echo Executable: build\win-unpacked\Password Manager.exe
            )
        )
    )
) else (
    echo No build folder found. Build may have failed.
)

echo.
pause
