// Preload script для безопасного взаимодействия между основным и рендер процессами
const { contextBridge, ipcRenderer } = require('electron');

// Экспортируем API для рендер процесса
contextBridge.exposeInMainWorld('electronAPI', {
    // Методы для работы с приложением
    getVersion: () => ipcRenderer.invoke('get-version'),
    
    // Методы для работы с окном
    minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
    maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
    closeWindow: () => ipcRenderer.invoke('close-window'),
    
    // Методы для работы с файлами (если понадобятся в будущем)
    openFile: () => ipcRenderer.invoke('open-file'),
    saveFile: (data) => ipcRenderer.invoke('save-file', data),
    
    // Уведомления
    showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body)
});

// Добавляем информацию о том, что это Electron приложение
window.isElectron = true;
window.electronVersion = process.versions.electron;
