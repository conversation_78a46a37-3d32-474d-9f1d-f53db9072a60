function TabManager({ tabs, onTabsUpdate, onClose }) {
    try {
        const [newTabName, setNewTabName] = React.useState('');
        const [editingTab, setEditingTab] = React.useState(null);
        const [editName, setEditName] = React.useState('');

        const handleAddTab = async (e) => {
            e.preventDefault();
            if (!newTabName.trim()) return;

            try {
                await saveTab({ name: newTabName.trim() });
                setNewTabName('');
                onTabsUpdate();
                showToast('Tab added successfully');
            } catch (error) {
                showToast('Failed to add tab', 'error');
            }
        };

        const handleEditTab = async (e) => {
            e.preventDefault();
            if (!editName.trim()) return;

            try {
                await updateTab(editingTab.id, { name: editName.trim(), isDefault: editingTab.isDefault });
                setEditingTab(null);
                setEditName('');
                onTabsUpdate();
                showToast('Tab updated successfully');
            } catch (error) {
                showToast('Failed to update tab', 'error');
            }
        };

        const handleDeleteTab = async (tab) => {
            // Проверяем, является ли вкладка защищенной
            if (tab.name === 'Банковские карты' || tab.name === 'Заметки') {
                showToast(`Нельзя удалить вкладку "${tab.name}"`, 'error');
                return;
            }

            if (confirm(`Are you sure you want to delete the "${tab.name}" tab?`)) {
                try {
                    await deleteTab(tab.id);
                    onTabsUpdate();
                    showToast('Tab deleted successfully');
                } catch (error) {
                    showToast('Failed to delete tab', 'error');
                }
            }
        };

        const startEdit = (tab) => {
            // Проверяем, является ли вкладка защищенной
            if (tab.name === 'Банковские карты' || tab.name === 'Заметки') {
                showToast(`Нельзя редактировать вкладку "${tab.name}"`, 'error');
                return;
            }
            setEditingTab(tab);
            setEditName(tab.name);
        };

        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" data-name="tab-manager" data-file="components/TabManager.js">
                <div className="glass-effect rounded-2xl p-6 w-full max-w-lg max-h-[80vh] overflow-y-auto">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-bold">Manage Tabs</h2>
                        <button onClick={onClose} className="text-gray-400 hover:text-white">
                            <i className="fas fa-times"></i>
                        </button>
                    </div>

                    <form onSubmit={handleAddTab} className="mb-6">
                        <div className="flex gap-2">
                            <input
                                type="text"
                                placeholder="New tab name"
                                value={newTabName}
                                onChange={(e) => setNewTabName(e.target.value)}
                                className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                            />
                            <button
                                type="submit"
                                className="px-4 py-2 btn-primary rounded-lg font-semibold"
                            >
                                Add
                            </button>
                        </div>
                    </form>

                    <div className="space-y-3">
                        {tabs.map(tab => (
                            <div key={tab.id} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                                {editingTab?.id === tab.id ? (
                                    <form onSubmit={handleEditTab} className="flex-1 flex gap-2">
                                        <input
                                            type="text"
                                            value={editName}
                                            onChange={(e) => setEditName(e.target.value)}
                                            className="flex-1 px-2 py-1 bg-gray-700 border border-gray-600 rounded input-focus outline-none"
                                        />
                                        <button type="submit" className="text-green-400 hover:text-green-300">
                                            <i className="fas fa-check"></i>
                                        </button>
                                        <button
                                            type="button"
                                            onClick={() => setEditingTab(null)}
                                            className="text-gray-400 hover:text-gray-300"
                                        >
                                            <i className="fas fa-times"></i>
                                        </button>
                                    </form>
                                ) : (
                                    <div className="flex items-center justify-between w-full">
                                        <span className="text-white">{tab.name}</span>
                                        <div className="flex gap-2">
                                            {tab.name !== 'Банковские карты' && tab.name !== 'Заметки' && (
                                                <button
                                                    onClick={() => startEdit(tab)}
                                                    className="text-blue-400 hover:text-blue-300"
                                                >
                                                    <i className="fas fa-edit"></i>
                                                </button>
                                            )}
                                            {tab.name !== 'Банковские карты' && tab.name !== 'Заметки' && (
                                                <button
                                                    onClick={() => handleDeleteTab(tab)}
                                                    className="text-red-400 hover:text-red-300"
                                                >
                                                    <i className="fas fa-trash"></i>
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('TabManager component error:', error);
        reportError(error);
    }
}