<!DOCTYPE html>
<html>
<head>
    <title>Create Icon</title>
</head>
<body>
    <canvas id="canvas" width="256" height="256" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon()">Download Icon as PNG</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 256, 256);
        gradient.addColorStop(0, '#3B82F6');
        gradient.addColorStop(1, '#1E40AF');
        
        // Draw background circle
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(128, 128, 120, 0, 2 * Math.PI);
        ctx.fill();
        
        // Draw border
        ctx.strokeStyle = '#1E293B';
        ctx.lineWidth = 4;
        ctx.stroke();
        
        // Draw shield
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.moveTo(128, 40);
        ctx.lineTo(180, 70);
        ctx.lineTo(180, 140);
        ctx.quadraticCurveTo(180, 180, 128, 200);
        ctx.quadraticCurveTo(76, 180, 76, 140);
        ctx.lineTo(76, 70);
        ctx.closePath();
        ctx.fill();
        
        // Draw lock body
        ctx.fillStyle = '#1E40AF';
        ctx.fillRect(108, 120, 40, 50);
        
        // Draw lock shackle
        ctx.strokeStyle = '#1E40AF';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.arc(128, 113, 13, Math.PI, 0, false);
        ctx.stroke();
        
        // Draw keyhole
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(128, 140, 6, 0, 2 * Math.PI);
        ctx.fill();
        ctx.fillRect(125, 140, 6, 15);
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
