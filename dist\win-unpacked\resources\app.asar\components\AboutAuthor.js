function AboutAuthor({ onClose }) {
    const openVkLink = () => {
        window.open('https://vk.com/neirotophcik', '_blank');
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-start justify-center pt-32 p-4 z-50">
            <div className="glass-effect rounded-2xl p-6 w-full max-w-md">
                <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold">Password Manager</h2>
                    <p className="text-gray-400 mt-2">
                        Автор софта: SMAK
                    </p>
                </div>

                <div className="flex justify-center mb-6">
                    <button
                        onClick={openVkLink}
                        className="flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-semibold transition-colors"
                    >
                        <i className="fab fa-vk"></i>
                        Мы ВКонтакте!
                    </button>
                </div>

                <div className="flex justify-center">
                    <button
                        onClick={onClose}
                        className="px-6 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors"
                    >
                        Закрыть
                    </button>
                </div>
            </div>
        </div>
    );
}