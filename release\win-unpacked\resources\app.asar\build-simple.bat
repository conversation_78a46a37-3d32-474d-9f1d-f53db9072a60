@echo off
echo ========================================
echo Password Manager - Simple Build Script
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found: 
node --version

echo.
echo Building application (without custom icon)...
echo.

echo Building unpacked version first...
call npx electron-builder --dir --config.directories.output=release

if %errorlevel% neq 0 (
    echo ERROR: Failed to build unpacked version
    pause
    exit /b 1
)

echo.
echo Unpacked version created successfully!
echo.

echo Building portable version...
call npx electron-builder --win --x64 --config.win.target=portable --config.directories.output=release

if %errorlevel% neq 0 (
    echo Warning: Failed to build portable version
    echo You can still use the unpacked version
) else (
    echo Portable version created successfully!
)

echo.
echo Building installer...
call npx electron-builder --win --x64 --config.win.target=nsis --config.directories.output=release

if %errorlevel% neq 0 (
    echo Warning: Failed to build installer
    echo You can still use the portable or unpacked version
) else (
    echo Installer created successfully!
)

echo.
echo ========================================
echo Build process completed!
echo ========================================
echo.

if exist "release" (
    echo Files created in 'release' folder:
    echo.
    
    if exist "release\*.exe" (
        echo Executable files:
        dir /b "release\*.exe"
        echo.
    )
    
    if exist "release\win-unpacked" (
        echo Unpacked version available in: release\win-unpacked\
        if exist "release\win-unpacked\Password Manager.exe" (
            echo Main executable: release\win-unpacked\Password Manager.exe
        )
        echo.
    )
    
    echo All available files:
    dir /b "release"
) else (
    echo No release folder found. Build may have failed.
)

echo.
echo You can now run your application!
echo.
pause
