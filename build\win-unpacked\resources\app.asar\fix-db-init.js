// Скрипт для исправления инициализации базы данных
document.addEventListener('DOMContentLoaded', () => {
    // Функция для проверки и исправления состояния базы данных
    const fixDatabaseState = () => {
        try {
            console.log('Проверка состояния базы данных...');
            
            // Проверяем наличие глобальной переменной db
            if (!window.db) {
                console.log('База данных не инициализирована, пробуем исправить...');
                
                // Пробуем открыть базу данных
                const request = indexedDB.open('passwordManagerDB', 8);
                
                request.onerror = (event) => {
                    console.error('Ошибка при открытии базы данных:', event);
                    showToast('Ошибка инициализации базы данных', 'error');
                };
                
                request.onsuccess = (event) => {
                    console.log('База данных успешно открыта');
                    window.db = event.target.result;
                    showToast('База данных восстановлена', 'success');
                    
                    // Перезагружаем страницу для корректной инициализации
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                };
            }
        } catch (error) {
            console.error('Ошибка при исправлении базы данных:', error);
        }
    };
    
    // Запускаем исправление через небольшую задержку
    setTimeout(fixDatabaseState, 1000);
});