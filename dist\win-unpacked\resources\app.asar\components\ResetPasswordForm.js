function ResetPasswordForm({ onCancel }) {
    const [newPassword, setNewPassword] = React.useState('');
    const [confirmPassword, setConfirmPassword] = React.useState('');
    const [loading, setLoading] = React.useState(false);
    const [showPassword, setShowPassword] = React.useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            if (newPassword !== confirmPassword) {
                showToast('Пароли не совпадают', 'error');
                setLoading(false);
                return;
            }
            
            if (newPassword.length < 6) {
                showToast('Пароль должен содержать не менее 6 символов', 'error');
                setLoading(false);
                return;
            }
            
            const hashedPassword = await hashPassword(newPassword);
            await saveMasterPassword(hashedPassword);
            showToast('Мастер-пароль успешно изменен');
            onCancel();
        } catch (error) {
            console.error('Error resetting password:', error);
            showToast('Произошла ошибка при сбросе пароля', 'error');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center p-4" data-name="reset-password-container">
            <div className="glass-effect rounded-2xl p-8 w-full max-w-md">
                <div className="text-center mb-8">
                    <i className="fas fa-key text-4xl text-blue-400 mb-4"></i>
                    <h1 className="text-2xl font-bold mb-2">Сброс пароля</h1>
                    <p className="text-gray-400">Создайте новый мастер-пароль</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="relative">
                        <input
                            type={showPassword ? "text" : "password"}
                            placeholder="Новый пароль"
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                            className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                            required
                        />
                        <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                        >
                            <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                        </button>
                    </div>

                    <div className="relative">
                        <input
                            type={showPassword ? "text" : "password"}
                            placeholder="Подтвердите пароль"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                            required
                        />
                        <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                        >
                            <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                        </button>
                    </div>

                    <div className="flex space-x-4">
                        <button
                            type="button"
                            onClick={onCancel}
                            className="w-1/2 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold"
                        >
                            Отмена
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="w-1/2 py-3 btn-primary rounded-lg font-semibold disabled:opacity-50"
                        >
                            {loading ? 'Обработка...' : 'Сбросить пароль'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}