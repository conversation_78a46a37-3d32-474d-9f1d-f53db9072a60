// Скрипт для сброса версии базы данных и принудительного обновления схемы
document.addEventListener('DOMContentLoaded', () => {
    // Проверяем, нужно ли сбросить базу данных
    const needReset = localStorage.getItem('needDbReset') === 'true';
    
    if (needReset) {
        console.log('Сбрасываем базу данных для обновления схемы...');
        
        // Удаляем флаг сброса
        localStorage.removeItem('needDbReset');
        
        // Удаляем базу данных
        const request = indexedDB.deleteDatabase('passwordManagerDB');
        
        request.onsuccess = () => {
            console.log('База данных успешно удалена, перезагружаем страницу...');
            // Перезагружаем страницу для создания новой базы данных
            window.location.reload();
        };
        
        request.onerror = (event) => {
            console.error('Ошибка при удалении базы данных:', event);
        };
    }
    // Удаляем автоматическую установку флага для сброса при следующей загрузке
});