// Отладочные функции для проверки состояния хранилища
function checkStorageState() {
    console.log('--- Storage State Check ---');
    
    // Проверка localStorage
    const localStoragePassword = localStorage.getItem('masterPasswordHash');
    console.log('Master password in localStorage:', !!localStoragePassword);
    
    // Проверка состояния авторизации
    const isLoggedIn = localStorage.getItem('passwordManagerLoggedIn');
    console.log('User logged in state:', isLoggedIn);
    
    // Проверка IndexedDB
    checkIndexedDB();
}

async function checkIndexedDB() {
    try {
        const request = indexedDB.open('passwordManagerDB');
        
        request.onsuccess = async (event) => {
            const db = event.target.result;
            console.log('IndexedDB opened successfully');
            
            if (db.objectStoreNames.contains('settings')) {
                const transaction = db.transaction(['settings'], 'readonly');
                const store = transaction.objectStore('settings');
                const request = store.get('masterPassword');
                
                request.onsuccess = () => {
                    console.log('Master password in IndexedDB:', !!request.result);
                    if (request.result) {
                        console.log('Master password value exists:', !!request.result.value);
                    }
                };
                
                request.onerror = (error) => {
                    console.error('Error reading from IndexedDB:', error);
                };
            } else {
                console.log('Settings store not found in IndexedDB');
            }
        };
        
        request.onerror = (error) => {
            console.error('Error opening IndexedDB:', error);
        };
    } catch (error) {
        console.error('Exception checking IndexedDB:', error);
    }
}

// Запускаем проверку при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(checkStorageState, 1000);
});