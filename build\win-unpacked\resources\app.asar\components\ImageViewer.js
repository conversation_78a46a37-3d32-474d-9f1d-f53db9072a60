function ImageViewer({ imageUrl, onClose }) {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50" onClick={onClose}>
            <div className="relative max-w-4xl max-h-[90vh]">
                <img 
                    src={imageUrl} 
                    alt="Увеличенное изображение" 
                    className="max-w-full max-h-[90vh] object-contain rounded-lg shadow-xl"
                    onClick={(e) => e.stopPropagation()}
                />
                <button 
                    className="absolute top-2 right-2 bg-gray-800 bg-opacity-70 text-white rounded-full w-8 h-8 flex items-center justify-center"
                    onClick={onClose}
                >
                    <i className="fas fa-times"></i>
                </button>
            </div>
        </div>
    );
}