function ClearDatabaseButton() {
    const [isClearing, setIsClearing] = React.useState(false);
    
    const clearDatabase = async () => {
        if (confirm('Вы уверены, что хотите удалить все пароли и данные?')) {
            setIsClearing(true);
            try {
                // Удаляем базу данных
                await new Promise((resolve, reject) => {
                    const request = indexedDB.deleteDatabase('passwordManagerDB');
                    request.onsuccess = () => {
                        console.log('База данных успешно удалена');
                        resolve();
                    };
                    request.onerror = (event) => {
                        console.error('Ошибка при удалении базы данных:', event.target.error);
                        reject(event.target.error);
                    };
                });
                
                showToast('База данных очищена. Страница будет перезагружена.');
                
                // Перезагружаем страницу через 2 секунды
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } catch (error) {
                console.error('Ошибка при очистке базы данных:', error);
                showToast('Ошибка при очистке базы данных', 'error');
                setIsClearing(false);
            }
        }
    };
    
    return (
        <button
            onClick={clearDatabase}
            disabled={isClearing}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
        >
            {isClearing ? 'Очистка...' : 'Очистить базу данных'}
        </button>
    );
}