directories:
  output: release
  buildResources: build
appId: com.passwordmanager.app
productName: Password Manager
files:
  - filter:
      - '**/*'
      - '!node_modules/**/*'
      - '!dist/**/*'
      - '!build/**/*'
      - '!*.md'
      - '!.git/**/*'
win:
  target: nsis
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Password Manager
portable:
  artifactName: PasswordManager-Portable-${version}.exe
electronVersion: 27.3.11
