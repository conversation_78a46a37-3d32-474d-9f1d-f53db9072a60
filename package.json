{"name": "password-manager", "version": "1.0.0", "description": "Secure Password Manager - Desktop Application", "main": "electron-main.js", "homepage": "./", "author": "Password Manager Team", "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-portable": "electron-builder --win --x64 --config.win.target=portable", "build-all": "npm run build-win && npm run build-portable", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "build": {"appId": "com.passwordmanager.app", "productName": "Password Manager", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "!dist/**/*", "!build/**/*", "!*.md", "!.git/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Password Manager"}, "portable": {"artifactName": "PasswordManager-Portable-${version}.exe"}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-store": "^8.1.0"}}