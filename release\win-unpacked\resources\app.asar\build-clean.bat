@echo off
echo ========================================
echo Password Manager - Clean Build Script
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found: 
node --version

echo.
echo Cleaning previous build...
if exist "dist" (
    echo Removing dist folder...
    timeout /t 2 /nobreak >nul
    rd /s /q "dist" 2>nul
    if exist "dist" (
        echo Warning: Could not remove dist folder completely
        echo Some files may be in use. Please close all applications and try again.
    )
)

echo.
echo Installing/updating dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Creating icon...
node create-simple-icon.js

echo.
echo Building portable version...
echo.

call npm run build-portable

if %errorlevel% neq 0 (
    echo ERROR: Failed to build portable version
    echo Trying alternative build method...
    
    echo.
    echo Building with pack command...
    call npm run pack
    
    if %errorlevel% neq 0 (
        echo ERROR: All build methods failed
        pause
        exit /b 1
    )
)

echo.
echo Building Windows installer...
call npm run build-win

if %errorlevel% neq 0 (
    echo Warning: Failed to build Windows installer
    echo Portable version should still be available
)

echo.
echo ========================================
echo Build process completed!
echo ========================================
echo.

if exist "dist" (
    echo Files created in 'dist' folder:
    dir /b "dist\*.exe" 2>nul
    if %errorlevel% neq 0 (
        echo No exe files found, checking for unpacked version...
        if exist "dist\win-unpacked" (
            echo Unpacked version available in: dist\win-unpacked\
        )
    )
) else (
    echo No dist folder found. Build may have failed.
)

echo.
pause
