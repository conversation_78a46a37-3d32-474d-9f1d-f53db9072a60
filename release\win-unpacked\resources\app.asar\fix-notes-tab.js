// Скрипт для исправления проблемы с вкладкой "Заметки"
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Ждем инициализации базы данных
        setTimeout(async () => {
            console.log('Проверка наличия вкладки "Заметки"...');
            
            if (!window.db) {
                console.error('База данных не инициализирована');
                return;
            }
            
            // Получаем текущие вкладки
            const transaction = db.transaction(['tabs'], 'readonly');
            const store = transaction.objectStore('tabs');
            const request = store.getAll();
            
            request.onsuccess = async () => {
                const tabs = request.result;
                console.log('Текущие вкладки:', tabs.map(t => t.name));
                
                // Проверяем, есть ли вкладка "Заметки"
                const notesTab = tabs.find(tab => tab.name === 'Заметки');
                
                if (!notesTab) {
                    console.log('Вкладка "Заметки" не найдена, добавляем...');
                    
                    // Добавляем вкладку "Заметки"
                    const writeTransaction = db.transaction(['tabs'], 'readwrite');
                    const writeStore = writeTransaction.objectStore('tabs');
                    
                    const newTab = { 
                        name: 'Заметки', 
                        isDefault: true, 
                        type: 'notes', 
                        locked: true 
                    };
                    
                    const addRequest = writeStore.add(newTab);
                    
                    addRequest.onsuccess = () => {
                        console.log('Вкладка "Заметки" успешно добавлена');
                        // Перезагружаем страницу для применения изменений
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    };
                    
                    addRequest.onerror = (error) => {
                        console.error('Ошибка при добавлении вкладки "Заметки":', error);
                    };
                } else {
                    console.log('Вкладка "Заметки" уже существует');
                    
                    // Проверяем, правильно ли настроена вкладка
                    if (!notesTab.type || notesTab.type !== 'notes' || !notesTab.locked) {
                        console.log('Обновляем настройки вкладки "Заметки"');
                        
                        const writeTransaction = db.transaction(['tabs'], 'readwrite');
                        const writeStore = writeTransaction.objectStore('tabs');
                        
                        const updatedTab = {
                            ...notesTab,
                            type: 'notes',
                            locked: true
                        };
                        
                        const updateRequest = writeStore.put(updatedTab);
                        
                        updateRequest.onsuccess = () => {
                            console.log('Вкладка "Заметки" успешно обновлена');
                            // Перезагружаем страницу для применения изменений
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        };
                        
                        updateRequest.onerror = (error) => {
                            console.error('Ошибка при обновлении вкладки "Заметки":', error);
                        };
                    }
                }
            };
            
            request.onerror = (error) => {
                console.error('Ошибка при получении вкладок:', error);
            };
        }, 1000);
    } catch (error) {
        console.error('Ошибка при исправлении вкладки "Заметки":', error);
    }
});