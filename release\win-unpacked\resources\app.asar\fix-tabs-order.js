// Скрипт для исправления порядка вкладок
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Ждем инициализации базы данных
        setTimeout(async () => {
            if (!window.db) {
                console.error('База данных не инициализирована');
                return;
            }
            
            // Получаем текущие вкладки
            const tabs = await getTabs();
            
            // Проверяем, является ли "Банковские карты" первой вкладкой
            const cardsTabIndex = tabs.findIndex(tab => tab.name === 'Банковские карты');
            
            if (cardsTabIndex > 0) {
                console.log('Перемещаем вкладку "Банковские карты" в начало списка');
                
                // Очищаем хранилище вкладок
                const transaction = db.transaction(['tabs'], 'readwrite');
                const store = transaction.objectStore('tabs');
                
                // Удаляем все вкладки
                await new Promise((resolve) => {
                    const request = store.clear();
                    request.onsuccess = () => resolve();
                });
                
                // Создаем новый порядок вкладок
                const newTabs = [
                    tabs.find(tab => tab.name === 'Банковские карты'),
                    ...tabs.filter(tab => tab.name !== 'Банковские карты')
                ];
                
                // Добавляем вкладки в новом порядке
                for (const tab of newTabs) {
                    if (tab) {
                        await new Promise((resolve) => {
                            const request = store.add({
                                name: tab.name,
                                isDefault: tab.isDefault,
                                type: tab.type
                            });
                            request.onsuccess = () => resolve();
                        });
                    }
                }
                
                console.log('Порядок вкладок исправлен');
                
                // Перезагружаем страницу для применения изменений
                window.location.reload();
            } else {
                console.log('Вкладка "Банковские карты" уже в начале списка');
            }
        }, 1000);
    } catch (error) {
        console.error('Ошибка при исправлении порядка вкладок:', error);
    }
});