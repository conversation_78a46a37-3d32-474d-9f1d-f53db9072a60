function DocumentForm({ onAdd, onCancel }) {
    try {
        const [formData, setFormData] = React.useState({
            title: 'Паспорт',
            passportSeries: '',
            passportNumber: '',
            passportIssueDate: '',
            passportIssuedBy: '',
            passportDepartmentCode: '',
            inn: '',
            snils: '',
            notes: '',
            fontFamily: 'Inter',
            fieldColors: {
                passportSeries: '#ffffff',
                passportNumber: '#ffffff',
                passportIssueDate: '#ffffff',
                passportIssuedBy: '#ffffff',
                passportDepartmentCode: '#ffffff',
                inn: '#ffffff',
                snils: '#ffffff',
                notes: '#ffffff'
            }
        });
        
        const [showData, setShowData] = React.useState(false);

        const handleSubmit = async (e) => {
            e.preventDefault();
            
            if (!formData.title) {
                showToast('Пожалуйста, укажите название документа', 'error');
                return;
            }

            try {
                await saveDocument(formData);
                showToast('Документ сохранен успешно');
                onAdd();
            } catch (error) {
                showToast('Ошибка при сохранении документа', 'error');
            }
        };

        const handleInputChange = (field, onlyDigits = false) => (e) => {
            let value = e.target.value;
            if (field === 'passportIssuedBy' || field === 'notes') {
                // Для этих полей разрешаем любой ввод
                setFormData({ ...formData, [field]: value });
            } else {
                // Для остальных полей только цифры
                const numericValue = value.replace(/[^0-9]/g, '');
                setFormData({ ...formData, [field]: numericValue });
            }
        };
        
        const handleColorChange = (field) => (e) => {
            setFormData({
                ...formData,
                fieldColors: {
                    ...formData.fieldColors,
                    [field]: e.target.value
                }
            });
        };
        
        const fontOptions = [
            { value: 'Inter', label: 'Inter (Default)' },
            { value: 'Arial', label: 'Arial' },
            { value: 'Helvetica', label: 'Helvetica' },
            { value: 'Times New Roman', label: 'Times New Roman' },
            { value: 'Courier New', label: 'Courier New' },
            { value: 'Georgia', label: 'Georgia' },
            { value: 'Verdana', label: 'Verdana' }
        ];

        return (
            <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center p-4 z-50" data-name="document-form-modal">
                <div className="glass-effect rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-xl font-bold">Добавить документ</h2>
                        <button onClick={onCancel} className="text-gray-400 hover:text-white">
                            <i className="fas fa-times"></i>
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="flex gap-3">
                            <div className="flex-1">
                                <div className="flex items-center mb-1">
                                    <label className="text-sm text-gray-400">Серия паспорта *</label>
                                    <input
                                        type="color"
                                        value={formData.fieldColors.passportSeries}
                                        onChange={handleColorChange('passportSeries')}
                                        className="h-5 w-5 rounded ml-2"
                                    />
                                </div>
                                <input
                                    type="text"
                                    placeholder="Серия паспорта *"
                                    value={formData.passportSeries}
                                    onChange={handleInputChange('passportSeries')}
                                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                    style={{color: formData.fieldColors.passportSeries}}
                                    required
                                />
                            </div>
                            <div className="flex-1">
                                <div className="flex items-center mb-1">
                                    <label className="text-sm text-gray-400">Номер паспорта *</label>
                                    <input
                                        type="color"
                                        value={formData.fieldColors.passportNumber}
                                        onChange={handleColorChange('passportNumber')}
                                        className="h-5 w-5 rounded ml-2"
                                    />
                                </div>
                                <input
                                    type="text"
                                    placeholder="Номер паспорта *"
                                    value={formData.passportNumber}
                                    onChange={handleInputChange('passportNumber')}
                                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                    style={{color: formData.fieldColors.passportNumber}}
                                    required
                                />
                            </div>
                        </div>

                        <div>
                            <div className="flex items-center mb-1">
                                <label className="text-sm text-gray-400">Дата выдачи *</label>
                                <input
                                    type="color"
                                    value={formData.fieldColors.passportIssueDate}
                                    onChange={handleColorChange('passportIssueDate')}
                                    className="h-5 w-5 rounded ml-2"
                                />
                            </div>
                            <input
                                type="text"
                                placeholder="Дата выдачи (ДД.ММ.ГГГГ) *"
                                value={formData.passportIssueDate}
                                onChange={handleInputChange('passportIssueDate')}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{color: formData.fieldColors.passportIssueDate}}
                                required
                            />
                        </div>

                        <div>
                            <div className="flex items-center mb-1">
                                <label className="text-sm text-gray-400">Кем выдан</label>
                                <input
                                    type="color"
                                    value={formData.fieldColors.passportIssuedBy}
                                    onChange={handleColorChange('passportIssuedBy')}
                                    className="h-5 w-5 rounded ml-2"
                                />
                            </div>
                            <input
                                type="text"
                                placeholder="Кем выдан"
                                value={formData.passportIssuedBy}
                                onChange={handleInputChange('passportIssuedBy')}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{color: formData.fieldColors.passportIssuedBy}}
                            />
                        </div>

                        <div>
                            <div className="flex items-center mb-1">
                                <label className="text-sm text-gray-400">Код подразделения *</label>
                                <input
                                    type="color"
                                    value={formData.fieldColors.passportDepartmentCode}
                                    onChange={handleColorChange('passportDepartmentCode')}
                                    className="h-5 w-5 rounded ml-2"
                                />
                            </div>
                            <input
                                type="text"
                                placeholder="Код подразделения *"
                                value={formData.passportDepartmentCode}
                                onChange={handleInputChange('passportDepartmentCode')}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                style={{color: formData.fieldColors.passportDepartmentCode}}
                                required
                            />
                        </div>

                        <div className="flex gap-3">
                            <div className="flex-1">
                                <div className="flex items-center mb-1">
                                    <label className="text-sm text-gray-400">ИНН</label>
                                    <input
                                        type="color"
                                        value={formData.fieldColors.inn}
                                        onChange={handleColorChange('inn')}
                                        className="h-5 w-5 rounded ml-2"
                                    />
                                </div>
                                <input
                                    type="text"
                                    placeholder="ИНН"
                                    value={formData.inn}
                                    onChange={handleInputChange('inn')}
                                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                    style={{color: formData.fieldColors.inn}}
                                />
                            </div>
                            <div className="flex-1">
                                <div className="flex items-center mb-1">
                                    <label className="text-sm text-gray-400">СНИЛС</label>
                                    <input
                                        type="color"
                                        value={formData.fieldColors.snils}
                                        onChange={handleColorChange('snils')}
                                        className="h-5 w-5 rounded ml-2"
                                    />
                                </div>
                                <input
                                    type="text"
                                    placeholder="СНИЛС"
                                    value={formData.snils}
                                    onChange={handleInputChange('snils')}
                                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                                    style={{color: formData.fieldColors.snils}}
                                />
                            </div>
                        </div>
                        
                        <div>
                            <label className="block text-sm text-gray-400 mb-1">Шрифт</label>
                            <select
                                value={formData.fontFamily}
                                onChange={(e) => setFormData({ ...formData, fontFamily: e.target.value })}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none"
                            >
                                {fontOptions.map(option => (
                                    <option key={option.value} value={option.value} style={{fontFamily: option.value}}>
                                        {option.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                        
                        <div>
                            <div className="flex items-center mb-1">
                                <label className="text-sm text-gray-400">Дополнительная информация</label>
                                <input
                                    type="color"
                                    value={formData.fieldColors.notes}
                                    onChange={handleColorChange('notes')}
                                    className="h-5 w-5 rounded ml-2"
                                />
                            </div>
                            <textarea
                                placeholder="Дополнительная информация"
                                value={formData.notes}
                                onChange={handleInputChange('notes')}
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg input-focus outline-none resize-none"
                                style={{color: formData.fieldColors.notes}}
                                rows="3"
                            ></textarea>
                        </div>

                        <div className="flex space-x-3 pt-4">
                            <button
                                type="submit"
                                className="flex-1 py-3 btn-primary rounded-lg font-semibold"
                            >
                                Сохранить
                            </button>
                            <button
                                type="button"
                                onClick={onCancel}
                                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg font-semibold transition-colors"
                            >
                                Отмена
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('DocumentForm component error:', error);
        reportError(error);
    }
}