// Компонент для управления сессией пользователя
function checkSession() {
    // Проверяем, был ли пользователь авторизован
    const isLoggedIn = localStorage.getItem('passwordManagerLoggedIn') === 'true';
    
    // Если пользователь закрыл браузер или вкладку, сбрасываем состояние при следующем открытии
    window.addEventListener('beforeunload', () => {
        // Можно раскомментировать следующую строку, чтобы всегда требовать повторный вход при закрытии вкладки
        // localStorage.removeItem('passwordManagerLoggedIn');
    });
    
    return isLoggedIn;
}

// Автоматический выход по таймауту бездействия (30 минут)
function setupSessionTimeout() {
    let inactivityTimer;
    const TIMEOUT = 30 * 60 * 1000; // 30 минут в миллисекундах
    
    function resetTimer() {
        clearTimeout(inactivityTimer);
        inactivityTimer = setTimeout(() => {
            // Автоматический выход по таймауту
            localStorage.removeItem('passwordManagerLoggedIn');
            showToast('Сессия завершена из-за бездействия');
            window.location.reload();
        }, TIMEOUT);
    }
    
    // Сбрасываем таймер при активности пользователя
    window.addEventListener('mousemove', resetTimer);
    window.addEventListener('keypress', resetTimer);
    window.addEventListener('click', resetTimer);
    window.addEventListener('scroll', resetTimer);
    
    // Инициализируем таймер
    resetTimer();
}

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    // Можно раскомментировать для включения автоматического выхода по таймауту
    // setupSessionTimeout();
});